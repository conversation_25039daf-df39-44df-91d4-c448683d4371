
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import AdminLayout from '@/layouts/AdminLayout';
import FormInput from '@/components/FormInput';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';

const AddDevice = () => {
  const [formData, setFormData] = useState({
    name: '',
    network: '',
    deviceType: '',
    operation: 'Operational',
    data: '',
  });

  const navigate = useNavigate();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // In a real app, you would call an API here
    alert('Device created successfully');
    navigate('/admin/devices');
  };

  // Sample data for dropdowns
  const networks = [
    { id: 1, name: 'Home Network' },
    { id: 2, name: 'Office Network' },
    { id: 3, name: 'IoT Network' },
    { id: 4, name: 'Development Network' },
    { id: 5, name: 'Production Network' },
  ];

  const deviceTypes = [
    { id: 1, name: 'Smart Thermostat' },
    { id: 2, name: 'Smart Light Bulb' },
    { id: 3, name: 'Smart Lock' },
    { id: 4, name: 'Security Camera' },
    { id: 5, name: 'Smart Speaker' },
  ];

  return (
    <AdminLayout>
      <div className="bg-white rounded-md shadow-sm p-6">
        <h2 className="text-xl font-medium mb-6">Create New Device</h2>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <FormInput
            label="Name"
            id="name"
            placeholder="Enter device name"
            value={formData.name}
            onChange={(e) => handleChange(e)}
            name="name"
            required
          />
          
          <div>
            <label className="block mb-2">Network</label>
            <select
              className="w-full p-2 border rounded"
              name="network"
              value={formData.network}
              onChange={handleChange}
              required
            >
              <option value="">Select Network</option>
              {networks.map((network) => (
                <option key={network.id} value={network.name}>
                  {network.name}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block mb-2">Device Type</label>
            <select
              className="w-full p-2 border rounded"
              name="deviceType"
              value={formData.deviceType}
              onChange={handleChange}
              required
            >
              <option value="">Select Device Type</option>
              {deviceTypes.map((deviceType) => (
                <option key={deviceType.id} value={deviceType.name}>
                  {deviceType.name}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block mb-2">Operation</label>
            <select
              className="w-full p-2 border rounded"
              name="operation"
              value={formData.operation}
              onChange={handleChange}
            >
              <option value="Operational">Operational</option>
              <option value="Blocked">Blocked</option>
              <option value="Maintenance">Maintenance</option>
            </select>
          </div>
          
          <div>
            <label className="block mb-2">Data (JSON format)</label>
            <Textarea
              className="w-full h-32"
              placeholder="Enter data (JSON format)"
              name="data"
              value={formData.data}
              onChange={handleChange}
            />
          </div>
          
          <div className="flex justify-end gap-2">
            <Button
              type="button"
              className="bg-red-500 hover:bg-red-600 text-white"
              onClick={() => navigate('/admin/devices')}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="bg-sems-primary hover:bg-sems-secondary text-white"
            >
              Save
            </Button>
          </div>
        </form>
      </div>
    </AdminLayout>
  );
};

export default AddDevice;
