
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';

interface HeaderProps {
  title: string;
  actionButton?: {
    label: string;
    onClick: () => void;
  };
}

const Header = ({ title, actionButton }: HeaderProps) => {
  const { logout } = useAuth();

  return (
    <div className="flex items-center justify-between p-4 border-b bg-white">
      <h1 className="text-2xl font-medium text-gray-800">
        Smart Energy Management System's ADMIN PANEL
      </h1>
      <div className="flex gap-2">
        {actionButton && (
          <Button 
            className="bg-sems-primary hover:bg-sems-secondary text-white"
            onClick={actionButton.onClick}
          >
            {actionButton.label}
          </Button>
        )}
        <Button 
          className="bg-sems-primary hover:bg-sems-secondary text-white"
          onClick={logout}
        >
          Logout
        </Button>
      </div>
    </div>
  );
};

export default Header;
