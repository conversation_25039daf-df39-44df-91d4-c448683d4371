// User types
export interface User {
  id: number;
  login: string;
  role: number;
  status: number;
  lastLogin: string | null;
  data: {
    jsonString: string;
  };
}

// Network types
export interface Network {
  id: number;
  name: string;
  description: string;
}

// Device types
export interface DeviceType {
  id: number;
  name: string;
  description: string;
}

export interface Device {
  id: number;
  name: string;
  guid: string;
  deviceTypeId: number;
  networkId: number;
  status: number;
  data: {
    jsonString: string;
  };
}

// Auth types
export interface LoginRequest {
  login: string;
  password: string;
}

export interface LoginResponse {
  accessToken: string;
  refreshToken: string;
}

export interface AuthContextType {
  isAuthenticated: boolean;
  token: string | null;
  refreshToken: string | null;
  login: (username: string, password: string) => Promise<void>;
  logout: () => void;
}

// API response types
export interface CountResponse {
  count: number;
}

// Token creation types
export interface TokenCreatePayload {
  userId: number;
  actions: any[];
  networkIds: string[];
  deviceTypeIds: string[];
}

// Dashboard types
export interface DashboardCounts {
  users: number;
  networks: number;
  devices: number;
  deviceTypes: number;
  plugins: number;
}
