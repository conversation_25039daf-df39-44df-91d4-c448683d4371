
import { useState } from 'react';
import AdminLayout from '@/layouts/AdminLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import TableActions from '@/components/TableActions';

// Sample data
const initialPlugins = [
  { id: 1, name: 'Temperature Monitor', version: '1.2.0', status: 'Active', description: 'Monitors temperature across devices' },
  { id: 2, name: 'Security Suite', version: '2.1.5', status: 'Active', description: 'Enhanced security features for the SEMS' },
  { id: 3, name: 'Energy Optimizer', version: '1.0.1', status: 'Inactive', description: 'Optimizes energy usage across all devices' },
  { id: 4, name: 'Data Analytics', version: '3.2.0', status: 'Active', description: 'Provides analytics and reporting tools' },
  { id: 5, name: 'Device Scheduler', version: '1.4.2', status: 'Active', description: 'Allows scheduling of device actions' },
  { id: 6, name: 'Voice Control', version: '2.0.0', status: 'Inactive', description: 'Adds voice control capabilities' },
  { id: 7, name: 'Weather Integration', version: '1.1.0', status: 'Active', description: 'Integrates weather data for intelligent automation' },
  { id: 8, name: 'Remote Access', version: '2.3.1', status: 'Active', description: 'Allows secure remote access to the system' },
  { id: 9, name: 'Video Manager', version: '1.5.0', status: 'Inactive', description: 'Manages video feeds from security cameras' },
  { id: 10, name: 'Backup Utility', version: '1.0.3', status: 'Active', description: 'Creates and manages system backups' },
];

const Plugins = () => {
  const [plugins] = useState(initialPlugins);
  const [searchTerm, setSearchTerm] = useState('');

  const filteredPlugins = plugins.filter(plugin =>
    plugin.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleAddPlugin = () => {
    alert('This would open a plugin upload interface');
  };

  const handleEditPlugin = (pluginId: number) => {
    alert(`Edit plugin with ID ${pluginId}`);
  };

  const handleDeletePlugin = (pluginId: number) => {
    alert(`Delete plugin with ID ${pluginId}`);
  };

  return (
    <AdminLayout
      actionButton={{
        label: 'Add New Plugin',
        onClick: handleAddPlugin,
      }}
    >
      <div className="bg-white rounded-md shadow-sm">
        <div className="p-4 border-b">
          <div className="flex items-center gap-2">
            <span>Search By Name:</span>
            <Input
              placeholder="Enter plugin name"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="max-w-xs"
            />
          </div>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 text-left">
              <tr>
                <th className="p-4 font-medium">Name</th>
                <th className="p-4 font-medium">Version</th>
                <th className="p-4 font-medium">Status</th>
                <th className="p-4 font-medium">Description</th>
                <th className="p-4 font-medium">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y">
              {filteredPlugins.map((plugin) => (
                <tr key={plugin.id} className="hover:bg-gray-50">
                  <td className="p-4">{plugin.name}</td>
                  <td className="p-4">{plugin.version}</td>
                  <td className="p-4">
                    <span
                      className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${
                        plugin.status === 'Active'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}
                    >
                      {plugin.status}
                    </span>
                  </td>
                  <td className="p-4">{plugin.description}</td>
                  <td className="p-4">
                    <TableActions
                      onEdit={() => handleEditPlugin(plugin.id)}
                      onDelete={() => handleDeletePlugin(plugin.id)}
                    />
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </AdminLayout>
  );
};

export default Plugins;
