
import { useState } from 'react';
import AdminLayout from '@/layouts/AdminLayout';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { apiRequest } from '@/utils/api';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';

// Add interfaces for token creation
interface TokenCreatePayload {
  userId: number;
  actions: any[];
  networkIds: string[];
  deviceTypeIds: string[];
}

const JWTTokens = () => {
  const [date, setDate] = useState<Date | undefined>(new Date());
  const [tokens, setTokens] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [userId, setUserId] = useState<number>(0);
  const [networkIds, setNetworkIds] = useState<string[]>([]);
  const [deviceTypeIds, setDeviceTypeIds] = useState<string[]>([]);
  const { toast } = useToast();
  const { token } = useAuth();

  const handleCreateTokens = async () => {
    if (!date) return;
    setIsLoading(true);
    
    try {
      // Create payload according to API documentation
      const payload: TokenCreatePayload = {
        userId: userId,
        actions: [{}], // Empty action object as shown in the docs
        networkIds: networkIds.length ? networkIds : ["string"],
        deviceTypeIds: deviceTypeIds.length ? deviceTypeIds : ["string"]
      };
      
      // Call the token creation endpoint
      const response = await apiRequest<{ accessToken: string }>('/auth/rest/token/create', {
        method: 'POST',
        body: JSON.stringify(payload),
      });
      
      // Add the new token to the list
      if (response.accessToken) {
        setTokens(prev => [...prev, response.accessToken]);
        
        toast({
          title: "Success",
          description: "Token created successfully!",
        });
      }
    } catch (error) {
      console.error('Error creating token:', error);
      toast({
        title: "Error",
        description: "Failed to create token. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Display current token from auth context
  const currentToken = token && (
    <div className="mb-6">
      <h3 className="text-lg font-medium mb-2">Your Current Session Token</h3>
      <div className="bg-gray-50 p-4 rounded-md">
        <div className="flex items-center gap-2">
          <code className="bg-gray-100 p-2 rounded text-xs flex-1 overflow-x-auto">
            {token}
          </code>
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              navigator.clipboard.writeText(token);
              toast({
                title: "Copied!",
                description: "Token copied to clipboard",
              });
            }}
          >
            Copy
          </Button>
        </div>
      </div>
    </div>
  );

  return (
    <AdminLayout>
      <div className="bg-white rounded-md shadow-sm p-6">
        <h2 className="text-xl font-medium mb-6">JWT Tokens Management</h2>
        
        {currentToken}
        
        <div className="mb-6">
          <h3 className="text-lg font-medium mb-2">Generate New Token</h3>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="border rounded-md p-2">
              <Calendar
                mode="single"
                selected={date}
                onSelect={setDate}
                className="border rounded-md"
                disabled={(date) => date < new Date()}
              />
            </div>
            
            <div className="flex items-start sm:items-center">
              <Button
                className="bg-sems-primary hover:bg-sems-secondary text-white"
                onClick={handleCreateTokens}
                disabled={isLoading}
              >
                {isLoading ? 'Creating...' : 'Create Token'}
              </Button>
            </div>
          </div>
        </div>
        
        {tokens.length > 0 && (
          <div>
            <h3 className="text-lg font-medium mb-2">Generated Tokens</h3>
            <div className="bg-gray-50 p-4 rounded-md">
              <ul className="space-y-2">
                {tokens.map((token, index) => (
                  <li key={index} className="flex items-center gap-2">
                    <code className="bg-gray-100 p-2 rounded text-xs flex-1 overflow-x-auto">
                      {token}
                    </code>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        navigator.clipboard.writeText(token);
                        toast({
                          title: "Copied!",
                          description: "Token copied to clipboard",
                        });
                      }}
                    >
                      Copy
                    </Button>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  );
};

export default JWTTokens;

