
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import AdminLayout from '@/layouts/AdminLayout';
import { Input } from '@/components/ui/input';
import TableActions from '@/components/TableActions';

// Sample data
const initialDevices = [
  { id: 1, name: 'Thermostat Living Room', network: 'Home Network', deviceType: 'Smart Thermostat', operation: 'Operational', data: '{"temperature":22.5,"humidity":45}' },
  { id: 2, name: 'Smart Light Bedroom', network: 'Office Network', deviceType: 'Smart Light Bulb', operation: 'Operational', data: '{"brightness":75,"color":"warm white"}' },
  { id: 3, name: 'Front Door Lock', network: 'Home Network', deviceType: 'Smart Lock', operation: 'Blocked', data: '{"status":"locked","battery":80}' },
  { id: 4, name: 'Office Security Camera', network: 'IoT Network', deviceType: 'Security Camera', operation: 'Operational', data: '{"resolution":"1080p","status":"active"}' },
  { id: 5, name: 'Kitchen Speaker', network: 'Home Network', deviceType: 'Smart Speaker', operation: 'Operational', data: '{"volume":50,"status":"idle"}' },
  { id: 6, name: 'Living Room Smart Plug', network: 'Office Network', deviceType: 'Smart Plug', operation: 'Blocked', data: '{"power":"off"}' },
  { id: 7, name: 'Smoke Detector Hallway', network: 'IoT Network', deviceType: 'Smart Smoke Detector', operation: 'Operational', data: '{"status":"normal","battery":90}' },
  { id: 8, name: 'Backyard Doorbell', network: 'Office Network', deviceType: 'Smart Doorbell', operation: 'Operational', data: '{"status":"active","battery":70}' },
  { id: 9, name: 'Smart Refrigerator', network: 'Development Network', deviceType: 'Smart Refrigerator', operation: 'Operational', data: '{"temperature":4,"status":"cooling"}' },
  { id: 10, name: 'Air Purifier Bedroom', network: 'Production Network', deviceType: 'Smart Air Purifier', operation: 'Blocked', data: '{"airQuality":"good","status":"running"}' },
];

const Devices = () => {
  const [devices] = useState(initialDevices);
  const [searchTerm, setSearchTerm] = useState('');
  const navigate = useNavigate();

  const filteredDevices = devices.filter(device =>
    device.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleAddDevice = () => {
    navigate('/admin/devices/add');
  };

  const handleDeviceDetails = (deviceId: number) => {
    navigate(`/admin/devices/${deviceId}`);
  };

  const handleDeleteDevice = (deviceId: number) => {
    // In a real app, you would call an API here
    alert(`Delete device with ID ${deviceId}`);
  };

  return (
    <AdminLayout
      actionButton={{
        label: 'Add New Device',
        onClick: handleAddDevice,
      }}
    >
      <div className="bg-white rounded-md shadow-sm">
        <div className="p-4 border-b">
          <div className="flex items-center gap-2">
            <span>Search By Name:</span>
            <Input
              placeholder="Enter device name"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="max-w-xs"
            />
          </div>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 text-left">
              <tr>
                <th className="p-4 font-medium">Name</th>
                <th className="p-4 font-medium">Network</th>
                <th className="p-4 font-medium">DeviceType</th>
                <th className="p-4 font-medium">Operation</th>
                <th className="p-4 font-medium">Data</th>
                <th className="p-4 font-medium">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y">
              {filteredDevices.map((device) => (
                <tr key={device.id} className="hover:bg-gray-50">
                  <td className="p-4">{device.name}</td>
                  <td className="p-4">{device.network}</td>
                  <td className="p-4">{device.deviceType}</td>
                  <td className="p-4">
                    <span
                      className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${
                        device.operation === 'Operational' 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}
                    >
                      {device.operation}
                    </span>
                  </td>
                  <td className="p-4 max-w-xs truncate" title={device.data}>
                    {device.data}
                  </td>
                  <td className="p-4">
                    <TableActions 
                      showDetails={true}
                      onDetails={() => handleDeviceDetails(device.id)}
                      onEdit={() => handleDeviceDetails(device.id)}
                      onDelete={() => handleDeleteDevice(device.id)}
                    />
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </AdminLayout>
  );
};

export default Devices;
