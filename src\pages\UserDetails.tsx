import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import AdminLayout from '@/layouts/AdminLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { api } from '@/utils/api';
import { toast } from '@/components/ui/sonner';
import { Checkbox } from '@/components/ui/checkbox';
import { PlusCircle, RefreshCw } from 'lucide-react';

interface User {
  id: number;
  login: string;
  role: number;
  status: number;
  lastLogin: string;
  data?: {
    jsonString: string;
  };
}

interface Network {
  id: number;
  name: string;
  description: string;
}

interface UserNetwork {
  id: number;
  name: string;
  description: string;
}

// Ajout des interfaces pour les types d'appareils
interface DeviceType {
  id: number;
  name: string;
  description: string;
}

const UserDetails = () => {
  const { userId } = useParams<{ userId: string }>();
  const [user, setUser] = useState<User | null>(null);
  const [networks, setNetworks] = useState<Network[]>([]);
  const [userNetworks, setUserNetworks] = useState<UserNetwork[]>([]);
  const [selectedNetwork, setSelectedNetwork] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    login: '',
    role: '',
    status: '',
    password: '',
    confirmPassword: '',
    data: ''
  });
  const navigate = useNavigate();

  // Ajout de l'état pour les types d'appareils
  const [deviceTypes, setDeviceTypes] = useState<DeviceType[]>([]);
  const [userDeviceTypes, setUserDeviceTypes] = useState<DeviceType[]>([]);
  const [selectedDeviceType, setSelectedDeviceType] = useState('');
  const [allDeviceTypesAccess, setAllDeviceTypesAccess] = useState(false);

  useEffect(() => {
    fetchUserDetails();
    fetchNetworks();
    fetchUserNetworks();
    fetchDeviceTypes();
    fetchUserDeviceTypes();
  }, [userId]);

  const fetchUserDetails = async () => {
    try {
      setIsLoading(true);
      const data = await api.get(`/user/${userId}`);
      setUser(data);
      setFormData({
        login: data.login || '',
        role: getRoleString(data.role),
        status: getStatusString(data.status),
        password: '',
        confirmPassword: '',
        data: data.data?.jsonString || '{}'
      });
    } catch (error) {
      console.error('Error fetching user details:', error);
      toast.error('Failed to load user details');
      navigate('/admin/users');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchNetworks = async () => {
    try {
      const data = await api.get('/network');
      setNetworks(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('Error fetching networks:', error);
    }
  };

  const fetchUserNetworks = async () => {
    try {
      // Récupérer la liste des réseaux auxquels l'utilisateur a accès
      const data = await api.get(`/user/${userId}/networks`);
      
      // Transformer les données selon le format de réponse de l'API
      let networks = [];
      
      if (Array.isArray(data)) {
        // Si la réponse est déjà un tableau de réseaux
        networks = data;
      } else if (data.networks && Array.isArray(data.networks)) {
        // Si la réponse contient un champ 'networks' qui est un tableau
        networks = data.networks;
      } else if (data.network) {
        // Si la réponse contient un objet 'network' unique (format de l'exemple)
        networks = [data.network];
      } else {
        // Aucun réseau trouvé
        networks = [];
      }
      
      setUserNetworks(networks);
    } catch (error) {
      console.error('Error fetching user networks:', error);
      setUserNetworks([]);
    }
  };

  // Fonction pour récupérer les détails d'un réseau spécifique
  const fetchNetworkDetails = async (networkId: number) => {
    try {
      // Utiliser l'endpoint GET /user/{id}/network/{networkId} pour obtenir les détails
      const data = await api.get(`/user/${userId}/network/${networkId}`);
      
      // Extraire les informations du réseau selon le format de réponse
      if (data && data.network) {
        return data.network;
      }
      
      return null;
    } catch (error) {
      console.error(`Error fetching network details for network ${networkId}:`, error);
      return null;
    }
  };

  // Fonction pour récupérer tous les types d'appareils
  const fetchDeviceTypes = async () => {
    try {
      const data = await api.get('/deviceTypes');
      setDeviceTypes(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('Error fetching device types:', error);
      setDeviceTypes([]);
    }
  };

  // Fonction pour récupérer les types d'appareils de l'utilisateur
  const fetchUserDeviceTypes = async () => {
    try {
      const data = await api.get(`/user/${userId}/devicetype`);
      
      // Vérifier le format de la réponse et extraire les types d'appareils
      let deviceTypesArray: DeviceType[] = [];
      
      if (data) {
        // Si la réponse contient un objet deviceType, l'extraire
        if (data.deviceType) {
          deviceTypesArray = [data.deviceType];
        } 
        // Si la réponse est un tableau, l'utiliser directement
        else if (Array.isArray(data)) {
          deviceTypesArray = data;
        }
        // Si la réponse est un objet avec une propriété deviceTypes
        else if (data.deviceTypes && Array.isArray(data.deviceTypes)) {
          deviceTypesArray = data.deviceTypes;
        }
      }
      
      setUserDeviceTypes(deviceTypesArray);
      
      // Vérifier si l'utilisateur a accès à tous les types d'appareils
      // La propriété peut être nommée hasUniversalAccess, allAccess, ou une autre variante
      setAllDeviceTypesAccess(
        data.hasUniversalAccess === true || 
        data.allAccess === true || 
        data.universalAccess === true
      );
    } catch (error) {
      console.error('Error fetching user device types:', error);
      setUserDeviceTypes([]);
      setAllDeviceTypesAccess(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSave = async () => {
    // Validate passwords match if provided
    if (formData.password && formData.password !== formData.confirmPassword) {
      toast.error('Passwords do not match');
      return;
    }

    try {
      // Validate JSON data if provided
      let jsonData = "{}";
      if (formData.data && formData.data.trim() !== '') {
        try {
          // Validate JSON format
          JSON.parse(formData.data);
          jsonData = formData.data;
        } catch (error) {
          toast.error('Invalid JSON format in data field');
          return;
        }
      }
      
      // Map role and status back to numbers
      const roleValue = formData.role === 'Administrator' ? 0 : 1;
      let statusValue = 0;
      if (formData.status === 'Locked') statusValue = 1;
      else if (formData.status === 'Disabled') statusValue = 2;
      
      // Prepare user data
      const userData: any = {
        login: formData.login,
        role: roleValue,
        status: statusValue,
        data: {
          jsonString: jsonData
        }
      };

      // Only include password if it was provided
      if (formData.password) {
        userData.password = formData.password;
      }

      await api.put(`/user/${userId}`, userData);
      toast.success('User updated successfully');
      setIsEditing(false);
      fetchUserDetails(); // Refresh user data
    } catch (error) {
      console.error('Error updating user:', error);
      toast.error('Failed to update user');
    }
  };

  const handleGrantAccess = async () => {
    if (!selectedNetwork) {
      toast.error('Please select a network');
      return;
    }

    try {
      // Utiliser la méthode PUT pour associer un utilisateur à un réseau
      // selon la documentation de l'API
      await api.put(`/user/${userId}/network/${selectedNetwork}`, {});
      toast.success('Access granted successfully');
      fetchUserNetworks(); // Refresh the list
      setSelectedNetwork('');
    } catch (error) {
      console.error('Error granting access:', error);
      toast.error('Failed to grant access');
    }
  };

  const handleRevokeAccess = async (networkId: number) => {
    if (confirm('Are you sure you want to revoke access to this network?')) {
      try {
        // Utiliser le nouvel endpoint pour supprimer l'association
        await api.delete(`/user/${userId}/network/${networkId}`);
        toast.success('Access revoked successfully');
        fetchUserNetworks(); // Refresh the list
      } catch (error) {
        console.error('Error revoking access:', error);
        toast.error('Failed to revoke access');
      }
    }
  };

  // Fonction pour accorder l'accès à un type d'appareil
  const handleGrantDeviceTypeAccess = async () => {
    if (!selectedDeviceType) {
      toast.error('Please select a device type');
      return;
    }

    try {
      await api.put(`/user/${userId}/deviceType/${selectedDeviceType}`, {});
      toast.success('Device type access granted successfully');
      fetchUserDeviceTypes();
      setSelectedDeviceType('');
    } catch (error) {
      console.error('Error granting device type access:', error);
      toast.error('Failed to grant device type access');
    }
  };

  // Fonction pour révoquer l'accès à un type d'appareil
  const handleRevokeDeviceTypeAccess = async (deviceTypeId: number) => {
    if (confirm('Are you sure you want to revoke access to this device type?')) {
      try {
        await api.delete(`/user/${userId}/deviceType/${deviceTypeId}`);
        toast.success('Device type access revoked successfully');
        fetchUserDeviceTypes();
      } catch (error) {
        console.error('Error revoking device type access:', error);
        toast.error('Failed to revoke device type access');
      }
    }
  };

  // Fonction pour gérer l'accès universel aux types d'appareils
  const handleUniversalDeviceTypeAccess = async (checked: boolean) => {
    try {
      if (checked) {
        // Accorder l'accès à tous les types d'appareils
        await api.put(`/user/${userId}/devicetype/all`, {});
        toast.success('Universal device type access granted');
      } else {
        // Révoquer l'accès à tous les types d'appareils
        await api.delete(`/user/${userId}/devicetype/all`);
        toast.success('Universal device type access revoked');
      }
      setAllDeviceTypesAccess(checked);
      fetchUserDeviceTypes();
    } catch (error) {
      console.error('Error updating universal device type access:', error);
      toast.error('Failed to update universal device type access');
      setAllDeviceTypesAccess(!checked); // Revert UI state on error
    }
  };

  // Fonction pour récupérer les détails d'un type d'appareil spécifique
  const fetchDeviceTypeDetails = async (deviceTypeId: number) => {
    try {
      const data = await api.get(`/devicetype/${deviceTypeId}`);
      
      // Extraire les informations du type d'appareil de la réponse
      let deviceType: DeviceType | null = null;
      
      if (data) {
        // Si la réponse contient directement un objet deviceType
        if (data.deviceType) {
          deviceType = data.deviceType;
        } 
        // Si la réponse est elle-même le deviceType (contient id, name, description)
        else if (data.id !== undefined) {
          deviceType = data;
        }
      }
      
      return deviceType;
    } catch (error) {
      console.error(`Error fetching device type ${deviceTypeId} details:`, error);
      toast.error('Failed to load device type details');
      return null;
    }
  };

  // Nouvelle fonction avec un nom différent
  const displayDeviceTypeDetails = async (deviceTypeId: number) => {
    const deviceType = await fetchDeviceTypeDetails(deviceTypeId);
    
    if (deviceType) {
      // Afficher les détails dans une notification toast
      toast.success(`Device Type: ${deviceType.name} - ${deviceType.description || 'No description available'}`);
    } else {
      toast.error('Device type details not found');
    }
  };

  // Helper function to convert role number to string
  const getRoleString = (role: number): string => {
    switch (role) {
      case 0: return 'Administrator';
      case 1: return 'Client';
      default: return 'Unknown';
    }
  };

  // Helper function to convert status number to string
  const getStatusString = (status: number): string => {
    switch (status) {
      case 0: return 'Active';
      case 1: return 'Locked';
      case 2: return 'Disabled';
      default: return 'Unknown';
    }
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="bg-white rounded-md shadow-sm p-6 text-center">
          Loading user details...
        </div>
      </AdminLayout>
    );
  }

  if (!user) {
    return (
      <AdminLayout>
        <div className="bg-white rounded-md shadow-sm p-6 text-center">
          User not found
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="bg-white rounded-md shadow-sm p-6 mb-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-medium">User Info</h2>
          <div className="flex gap-2">
            {isEditing ? (
              <>
                <Button 
                  onClick={() => setIsEditing(false)}
                  className="bg-red-500 hover:bg-red-600 text-white"
                >
                  Cancel
                </Button>
                <Button 
                  onClick={handleSave}
                  className="bg-green-500 hover:bg-green-600 text-white"
                >
                  Save
                </Button>
              </>
            ) : (
              <Button 
                onClick={() => setIsEditing(true)}
                className="bg-sems-primary hover:bg-sems-secondary text-white"
              >
                Edit User
              </Button>
            )}
          </div>
        </div>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm text-gray-500 mb-1">Login</label>
            {isEditing ? (
              <Input 
                name="login"
                value={formData.login}
                onChange={handleChange}
                className="w-full"
              />
            ) : (
              <p className="p-2 border rounded bg-gray-50">{user.login}</p>
            )}
          </div>
          
          <div>
            <label className="block text-sm text-gray-500 mb-1">Role</label>
            {isEditing ? (
              <select
                name="role"
                value={formData.role}
                onChange={handleChange}
                className="w-full p-2 border rounded"
              >
                <option value="Administrator">Administrator</option>
                <option value="Client">Client</option>
              </select>
            ) : (
              <p className="p-2 border rounded bg-gray-50">{getRoleString(user.role)}</p>
            )}
          </div>
          
          <div>
            <label className="block text-sm text-gray-500 mb-1">Status</label>
            {isEditing ? (
              <select
                name="status"
                value={formData.status}
                onChange={handleChange}
                className="w-full p-2 border rounded"
              >
                <option value="Active">Active</option>
                <option value="Locked">Locked</option>
                <option value="Disabled">Disabled</option>
              </select>
            ) : (
              <p className="p-2 border rounded bg-gray-50">{getStatusString(user.status)}</p>
            )}
          </div>
          
          {isEditing && (
            <>
              <div>
                <label className="block text-sm text-gray-500 mb-1">Password</label>
                <Input 
                  type="password"
                  name="password"
                  value={formData.password}
                  onChange={handleChange}
                  placeholder="Leave blank to keep unchanged"
                  className="w-full"
                />
              </div>
              
              <div>
                <label className="block text-sm text-gray-500 mb-1">Confirm Password</label>
                <Input 
                  type="password"
                  name="confirmPassword"
                  value={formData.confirmPassword}
                  onChange={handleChange}
                  placeholder="Confirm password"
                  className="w-full"
                  disabled={!formData.password}
                />
              </div>
            </>
          )}
          
          <div>
            <label className="block text-sm text-gray-500 mb-1">Data</label>
            {isEditing ? (
              <Textarea
                name="data"
                value={formData.data}
                onChange={handleChange}
                className="w-full h-32"
              />
            ) : (
              <pre className="p-2 border rounded bg-gray-50 overflow-auto h-32">
                {user.data?.jsonString || '{}'}
              </pre>
            )}
          </div>
        </div>
      </div>
      
      {/* Networks Section */}
      <div className="bg-white rounded-md shadow-sm p-6">
        <h2 className="text-xl font-medium mb-6">Networks</h2>
        
        <table className="w-full border-collapse mb-6">
          <thead>
            <tr className="bg-gray-50">
              <th className="p-3 text-left font-medium">Network</th>
              <th className="p-3 text-left font-medium">Description</th>
              <th className="p-3 text-center font-medium">Actions</th>
            </tr>
          </thead>
          <tbody>
            {userNetworks.length > 0 ? (
              userNetworks.map((network) => (
                <tr key={network.id} className="border-t">
                  <td className="p-3">{network.name}</td>
                  <td className="p-3">{network.description}</td>
                  <td className="p-3 text-center">
                    <Button 
                      size="sm" 
                      className="bg-red-500 hover:bg-red-600 text-white"
                      onClick={() => handleRevokeAccess(network.id)}
                    >
                      Revoke Access
                    </Button>
                  </td>
                </tr>
              ))
            ) : (
              <tr className="border-t">
                <td colSpan={3} className="p-3 text-center">
                  This user doesn't have access to any networks.
                </td>
              </tr>
            )}
          </tbody>
        </table>
        
        <div className="flex items-center gap-2">
          <div className="text-sm font-medium">Grant Access to Network:</div>
          <select
            className="p-2 border rounded flex-grow"
            value={selectedNetwork}
            onChange={(e) => setSelectedNetwork(e.target.value)}
          >
            <option value="">Select Network</option>
            {networks
              .filter(network => !userNetworks.some(un => un.id === network.id))
              .map((network) => (
                <option key={network.id} value={network.id.toString()}>
                  {network.name}
                </option>
              ))}
          </select>
          <Button 
            className="bg-gray-200 hover:bg-gray-300 text-gray-800"
            onClick={() => setSelectedNetwork('')}
          >
            Clear
          </Button>
          <Button 
            className="bg-green-600 hover:bg-green-700 text-white"
            onClick={handleGrantAccess}
          >
            Grant Access
          </Button>
        </div>
      </div>

      {/* Device Types Section */}
      <div className="bg-white rounded-md shadow-sm p-6 mt-6">
        <h2 className="text-xl font-medium mb-6">Device Types</h2>
        
        <div className="mb-4 flex items-center">
          <Checkbox 
            id="allDeviceTypes" 
            checked={allDeviceTypesAccess}
            onCheckedChange={handleUniversalDeviceTypeAccess}
          />
          <label 
            htmlFor="allDeviceTypes" 
            className="ml-2 text-sm font-medium"
          >
            All device types are available to user
          </label>
        </div>
        
        {!allDeviceTypesAccess && (
          <>
            <table className="w-full border-collapse mb-6">
              <thead>
                <tr className="bg-gray-50">
                  <th className="p-3 text-left font-medium">Device Type</th>
                  <th className="p-3 text-left font-medium">Description</th>
                  <th className="p-3 text-center font-medium">Actions</th>
                </tr>
              </thead>
              <tbody>
                {userDeviceTypes.length > 0 ? (
                  userDeviceTypes.map((deviceType) => (
                    <tr key={deviceType.id} className="border-t">
                      <td className="p-3">{deviceType.name}</td>
                      <td className="p-3">{deviceType.description}</td>
                      <td className="p-3 text-center">
                        <Button 
                          size="sm" 
                          className="bg-red-500 hover:bg-red-600 text-white"
                          onClick={() => handleRevokeDeviceTypeAccess(deviceType.id)}
                        >
                          Revoke Access
                        </Button>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr className="border-t">
                    <td colSpan={3} className="p-3 text-center">
                      This user doesn't have access to any device types.
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
            
            <div className="flex items-center gap-2">
              <div className="text-sm font-medium">Grant Access to Device Type:</div>
              <select
                className="p-2 border rounded flex-grow"
                value={selectedDeviceType}
                onChange={(e) => setSelectedDeviceType(e.target.value)}
              >
                <option value="">Select Device Type</option>
                {deviceTypes
                  .filter(deviceType => !userDeviceTypes.some(udt => udt.id === deviceType.id))
                  .map((deviceType) => (
                    <option key={deviceType.id} value={deviceType.id.toString()}>
                      {deviceType.name}
                    </option>
                  ))}
              </select>
              <Button 
                className="bg-gray-200 hover:bg-gray-300 text-gray-800"
                onClick={() => setSelectedDeviceType('')}
              >
                Clear
              </Button>
              <Button 
                className="bg-green-600 hover:bg-green-700 text-white"
                onClick={handleGrantDeviceTypeAccess}
              >
                Grant Access
              </Button>
            </div>
          </>
        )}
      </div>
    </AdminLayout>
  );
};

export default UserDetails;
















