
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import AdminLayout from '@/layouts/AdminLayout';
import { Input } from '@/components/ui/input';
import TableActions from '@/components/TableActions';
import { api } from '@/utils/api';
import { toast } from '@/components/ui/sonner';

interface Network {
  id: number;
  name: string;
  description: string;
}

const Networks = () => {
  const [networks, setNetworks] = useState<Network[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    fetchNetworks();
  }, []);

  const fetchNetworks = async () => {
    try {
      setIsLoading(true);
      const data = await api.get('/network');
      console.log('Networks data:', data);
      setNetworks(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('Error fetching networks:', error);
      toast.error('Failed to load networks');
    } finally {
      setIsLoading(false);
    }
  };

  const filteredNetworks = networks.filter(network =>
    network.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleAddNetwork = () => {
    navigate('/admin/networks/add');
  };

  const handleEditNetwork = (networkId: number) => {
    navigate(`/admin/networks/${networkId}`);
  };

  const handleDeleteNetwork = async (networkId: number) => {
    if (confirm('Are you sure you want to delete this network?')) {
      try {
        await api.delete(`/network/${networkId}`);
        toast.success('Network deleted successfully');
        fetchNetworks(); // Refresh the list
      } catch (error) {
        console.error('Error deleting network:', error);
        toast.error('Failed to delete network');
      }
    }
  };

  return (
    <AdminLayout
      actionButton={{
        label: 'Add New Network',
        onClick: handleAddNetwork,
      }}
    >
      <div className="bg-white rounded-md shadow-sm">
        <div className="p-4 border-b">
          <div className="flex items-center gap-2">
            <span>Search By Name:</span>
            <Input
              placeholder="Enter network name"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="max-w-xs"
            />
          </div>
        </div>
        <div className="overflow-x-auto">
          {isLoading ? (
            <div className="p-8 text-center">Loading networks...</div>
          ) : (
            <table className="w-full">
              <thead className="bg-gray-50 text-left">
                <tr>
                  <th className="p-4 font-medium">Name</th>
                  <th className="p-4 font-medium">Description</th>
                  <th className="p-4 font-medium">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y">
                {filteredNetworks.length > 0 ? (
                  filteredNetworks.map((network) => (
                    <tr key={network.id} className="hover:bg-gray-50">
                      <td className="p-4">{network.name}</td>
                      <td className="p-4">{network.description}</td>
                      <td className="p-4">
                        <TableActions 
                          onEdit={() => handleEditNetwork(network.id)}
                          onDelete={() => handleDeleteNetwork(network.id)}
                          showEdit={true}
                        />
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={3} className="p-4 text-center">
                      {searchTerm ? 'No networks match your search' : 'No networks found'}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          )}
        </div>
      </div>
    </AdminLayout>
  );
};

export default Networks;

