
import { ReactNode } from 'react';

interface DashboardCardProps {
  title: string;
  count: number;
  icon: ReactNode;
}

const DashboardCard = ({ title, count, icon }: DashboardCardProps) => {
  return (
    <div className="bg-white rounded-md shadow-sm p-6 hover:shadow-md transition">
      <div className="flex flex-col items-center justify-center text-center gap-3">
        <div className="text-sems-primary">
          {icon}
        </div>
        <div>
          <h3 className="text-gray-700">{title}</h3>
          <span className="text-xl font-semibold">{count}</span>
        </div>
      </div>
    </div>
  );
};

export default DashboardCard;
