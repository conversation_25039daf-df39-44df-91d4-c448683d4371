import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import AdminLayout from '@/layouts/AdminLayout';
import FormInput from '@/components/FormInput';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { api } from '@/utils/api';
import { toast } from '@/components/ui/sonner';

interface NetworkData {
  name: string;
  description: string;
}

const EditNetwork = () => {
  const { id } = useParams<{ id: string }>();
  const [formData, setFormData] = useState<NetworkData>({
    name: '',
    description: '',
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    fetchNetwork();
  }, [id]);

  const fetchNetwork = async () => {
    try {
      setIsLoading(true);
      const data = await api.get(`/network/${id}`);
      console.log('Network details:', data);
      setFormData({
        name: data.name || '',
        description: data.description || '',
      });
    } catch (error) {
      console.error('Error fetching network:', error);
      toast.error('Failed to load network details');
      navigate('/admin/networks');
    } finally {
      setIsLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      toast.error('Network name is required');
      return;
    }
    
    try {
      setIsSubmitting(true);
      
      // Call API to update network
      await api.put(`/network/${id}`, formData);
      
      toast.success('Network updated successfully');
      navigate('/admin/networks');
    } catch (error: any) {
      console.error('Error updating network:', error);
      
      // Check for duplicate name error
      if (error.message && error.message.includes('already exists')) {
        toast.error('A network with this name already exists');
      } else {
        toast.error(`Failed to update network: ${error.message || 'Unknown error'}`);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="bg-white rounded-md shadow-sm p-6 text-center">
          Loading network details...
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="bg-white rounded-md shadow-sm p-6">
        <h2 className="text-xl font-medium mb-6">Edit Network</h2>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <FormInput
            label="Name"
            id="name"
            placeholder="Enter network name"
            value={formData.name}
            onChange={(e) => handleChange(e)}
            name="name"
            required
          />
          
          <div>
            <label className="block mb-2">Description</label>
            <Textarea
              className="w-full h-32"
              placeholder="Enter network description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              required
            />
          </div>
          
          <div className="flex justify-end gap-2">
            <Button
              type="button"
              className="bg-red-500 hover:bg-red-600 text-white"
              onClick={() => navigate('/admin/networks')}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="bg-sems-primary hover:bg-sems-secondary text-white"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Saving...' : 'Save'}
            </Button>
          </div>
        </form>
      </div>
    </AdminLayout>
  );
};

export default EditNetwork;
