

export const API_BASE_URL = 'http://localhost:80/api/rest';

export async function apiRequest<T = any>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const token = await AsyncStorage.getItem('sems-token');
  const headers = {
    'Content-Type': 'application/json',
    ...(token && { 'Authorization': `Bearer ${token}` }),
    ...options.headers,
  };

  // Construct the full URL
  const url = endpoint.startsWith('/auth') 
    ? `http://localhost:80${endpoint}` // Auth endpoints use different base path
    : `${API_BASE_URL}${endpoint}`;     // Regular API endpoints

  console.log(`Making API request to: ${url}`, options);

  try {
    const response = await fetch(url, {
      ...options,
      headers,
    });

    console.log(`Response status: ${response.status}`);

    if (!response.ok) {
      // Get error details from response if possible
      let errorMessage = `API error: ${response.status} ${response.statusText}`;
      try {
        const errorData = await response.json();
        errorMessage = errorData.message || errorMessage;
        console.error('API error details:', errorData);
      } catch (e) {
        // If we can't parse the error as JSON, just use the status text
      }

      throw new Error(errorMessage);
    }

    // For endpoints that don't return JSON
    if (response.status === 204) {
      return {} as T;
    }

    const data = await response.json();
    console.log('API response data:', data);
    return data;
  } catch (error) {
    console.error('API request failed:', error);
    throw error;
  }
}

// Helper functions for common API operations
export const api = {
  get: <T = any>(endpoint: string, options: RequestInit = {}) => 
    apiRequest<T>(endpoint, { ...options, method: 'GET' }),
    
  post: <T = any>(endpoint: string, data: any, options: RequestInit = {}) =>
    apiRequest<T>(endpoint, { 
      ...options, 
      method: 'POST',
      body: JSON.stringify(data)
    }),
    
  put: <T = any>(endpoint: string, data: any, options: RequestInit = {}) =>
    apiRequest<T>(endpoint, { 
      ...options, 
      method: 'PUT',
      body: JSON.stringify(data)
    }),
    
  delete: <T = any>(endpoint: string, options: RequestInit = {}) =>
    apiRequest<T>(endpoint, { ...options, method: 'DELETE' }),
    
  // Specific function to get a user by ID
  getUser: (userId: number | string) => api.get(`/user/${userId}`)
};







