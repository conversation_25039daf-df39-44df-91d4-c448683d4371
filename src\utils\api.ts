
export const API_BASE_URL = 'http://localhost:80/api/rest';

export async function apiRequest<T = any>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const token = localStorage.getItem('sems-token');
  const headers = {
    'Content-Type': 'application/json',
    ...(token && { 'Authorization': `Bearer ${token}` }),
    ...options.headers,
  };

  // Construct the full URL
  const url = endpoint.startsWith('/auth') 
    ? `http://localhost:80${endpoint}` // Auth endpoints use different base path
    : `${API_BASE_URL}${endpoint}`;     // Regular API endpoints

  console.log(`Making API request to: ${url}`, options);

  try {
    const response = await fetch(url, {
      ...options,
      headers,
    });

    console.log(`Response status: ${response.status}`);

    if (!response.ok) {
      // Get error details from response if possible
      let errorMessage = `API error: ${response.status} ${response.statusText}`;
      try {
        const errorData = await response.json();
        errorMessage = errorData.message || errorMessage;
        console.error('API error details:', errorData);
      } catch (e) {
        // If we can't parse the error as JSON, just use the status text
      }

      throw new Error(errorMessage);
    }

    // For endpoints that don't return JSON
    if (response.status === 204) {
      return {} as T;
    }

    const data = await response.json();
    console.log('API response data:', data);
    return data;
  } catch (error) {
    console.error('API request failed:', error);
    throw error;
  }
}

// Helper functions for common API operations
export const api = {
  get: <T = any>(endpoint: string, options: RequestInit = {}) => 
    apiRequest<T>(endpoint, { ...options, method: 'GET' }),
    
  post: <T = any>(endpoint: string, data: any, options: RequestInit = {}) =>
    apiRequest<T>(endpoint, { 
      ...options, 
      method: 'POST',
      body: JSON.stringify(data)
    }),
    
  put: <T = any>(endpoint: string, data: any, options: RequestInit = {}) =>
    apiRequest<T>(endpoint, { 
      ...options, 
      method: 'PUT',
      body: JSON.stringify(data)
    }),
    
  delete: <T = any>(endpoint: string, options: RequestInit = {}) =>
    apiRequest<T>(endpoint, { ...options, method: 'DELETE' }),
    
  // Specific function to get a user by ID
  getUser: (userId: number | string) => api.get(`/user/${userId}`),

  // Device notification endpoints
  getDeviceNotification: (deviceId: number | string, notificationId: number | string) =>
    api.get(`/device/${deviceId}/notification/${notificationId}`),

  getDeviceNotifications: (deviceId: number | string, params?: {
    sortField?: string;
    take?: number;
    skip?: number;
    sortOrder?: 'asc' | 'desc';
  }) => {
    const queryParams = new URLSearchParams();
    if (params?.sortField) queryParams.append('sortField', params.sortField);
    if (params?.take) queryParams.append('take', params.take.toString());
    if (params?.skip) queryParams.append('skip', params.skip.toString());
    if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);

    const queryString = queryParams.toString();
    const endpoint = `/device/${deviceId}/notification${queryString ? `?${queryString}` : ''}`;
    return api.get(endpoint);
  },

  // Poll for device notifications with long polling
  pollDeviceNotifications: (deviceId: number | string, waitTimeout: number = 30) =>
    api.get(`/device/${deviceId}/notification/poll?waitTimeout=${waitTimeout}`),

  // Poll for all device notifications with long polling
  pollAllDeviceNotifications: (waitTimeout: number = 30) =>
    api.get(`/device/notification/poll?waitTimeout=${waitTimeout}`),

  createDeviceNotification: (deviceId: number | string, data: any) =>
    api.post(`/device/${deviceId}/notification`, data),

  updateDeviceNotification: (deviceId: number | string, notificationId: number | string, data: any) =>
    api.put(`/device/${deviceId}/notification/${notificationId}`, data),

  deleteDeviceNotification: (deviceId: number | string, notificationId: number | string) =>
    api.delete(`/device/${deviceId}/notification/${notificationId}`),

  // Device command endpoints
  getDeviceCommand: (deviceId: number | string, commandId: number | string, returnUpdatedCommands: boolean = false) =>
    api.get(`/device/${deviceId}/command/${commandId}?returnUpdatedCommands=${returnUpdatedCommands}`),
  
  getDeviceCommands: (deviceId: number | string, params?: {
    returnUpdatedCommands?: boolean;
    sortField?: string;
    take?: number;
    skip?: number;
    sortOrder?: 'asc' | 'desc';
  }) => {
    const queryParams = new URLSearchParams();
    if (params?.returnUpdatedCommands !== undefined) queryParams.append('returnUpdatedCommands', params.returnUpdatedCommands.toString());
    if (params?.sortField) queryParams.append('sortField', params.sortField);
    if (params?.take) queryParams.append('take', params.take.toString());
    if (params?.skip) queryParams.append('skip', params.skip.toString());
    if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);
    
    const queryString = queryParams.toString();
    const endpoint = `/device/${deviceId}/command${queryString ? `?${queryString}` : ''}`;
    return api.get(endpoint);
  },

  createDeviceCommand: (deviceId: number | string, data: any) =>
    api.post(`/device/${deviceId}/command`, data),
  
  updateDeviceCommand: (deviceId: number | string, commandId: number | string, data: any, returnUpdatedCommands: boolean = false) =>
    api.put(`/device/${deviceId}/command/${commandId}?returnUpdatedCommands=${returnUpdatedCommands}`, data),
  
  deleteDeviceCommand: (deviceId: number | string, commandId: number | string) =>
    api.delete(`/device/${deviceId}/command/${commandId}`)
};











