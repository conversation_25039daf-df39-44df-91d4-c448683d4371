
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import AdminLayout from '@/layouts/AdminLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import TableActions from '@/components/TableActions';
import { toast } from '@/components/ui/sonner';
import { api } from '@/utils/api';

interface User {
  id: number;
  login: string;
  role: number;
  status: number;
  lastLogin: string | null;
  data: {
    jsonString: string;
  };
}

const Users = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const navigate = useNavigate();

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const response = await api.get('/user');
      setUsers(response);
    } catch (error) {
      console.error('Error fetching users:', error);
      toast.error('Failed to load users');
    } finally {
      setLoading(false);
    }
  };

  const handleAddUser = () => {
    navigate('/admin/users/add');
  };

  const handleUserDetails = (userId: number) => {
    navigate(`/admin/users/${userId}`);
  };

  // Suppression de la fonction handleEditUser

  const handleDeleteUser = async (userId: number) => {
    if (!confirm('Are you sure you want to delete this user?')) {
      return;
    }

    try {
      await api.delete(`/user/${userId}`);
      toast.success('User deleted successfully');
      fetchUsers(); // Refresh the list
    } catch (error) {
      console.error('Error deleting user:', error);
      toast.error('Failed to delete user');
    }
  };

  // Filter users based on search term
  const filteredUsers = users.filter(user => 
    user.login.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Helper function to convert role number to string
  const getRoleString = (role: number): string => {
    switch (role) {
      case 0: return 'Administrator';
      case 1: return 'Client';
      default: return 'Unknown';
    }
  };

  // Helper function to convert status number to string
  const getStatusString = (status: number): string => {
    switch (status) {
      case 0: return 'Active';
      case 1: return 'Locked';
      case 2: return 'Disabled';
      default: return 'Unknown';
    }
  };

  // Format the last login date
  const formatLastLogin = (lastLogin: string | null): string => {
    if (!lastLogin) return 'Never';
    try {
      return new Date(lastLogin).toLocaleString();
    } catch (e) {
      return lastLogin;
    }
  };

  return (
    <AdminLayout
      actionButton={{
        label: "Add User",
        onClick: handleAddUser
      }}
    >
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <div className="w-64">
            <Input
              placeholder="Search users..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <div className="text-sm text-gray-500">
            Total users: {users.length}
          </div>
        </div>

        <div className="bg-white rounded-md shadow overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Login
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Role
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Last Login
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Data
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {loading ? (
                <tr>
                  <td colSpan={6} className="px-6 py-4 text-center">
                    Loading users...
                  </td>
                </tr>
              ) : filteredUsers.length === 0 ? (
                <tr>
                  <td colSpan={6} className="px-6 py-4 text-center">
                    No users found
                  </td>
                </tr>
              ) : (
                filteredUsers.map((user) => (
                  <tr key={user.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {user.login}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getRoleString(user.role)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${
                          user.status === 0 
                            ? 'bg-green-100 text-green-800' 
                            : user.status === 1 
                            ? 'bg-orange-100 text-orange-800' 
                            : 'bg-red-100 text-red-800'
                        }`}
                      >
                        {getStatusString(user.status)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {formatLastLogin(user.lastLogin)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {user.data?.jsonString ? '{}' : '{}'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <TableActions 
                        showDetails={true}
                        showEdit={false}
                        showDelete={true}
                        detailsUrl={`/admin/users/${user.id}`}
                        onDelete={() => handleDeleteUser(user.id)}
                      />
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </AdminLayout>
  );
};

export default Users;













