
import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';
import { ReactNode } from 'react';

interface ModalProps {
  title: string;
  isOpen: boolean;
  onClose: () => void;
  onSave?: () => void;
  onCancel?: () => void;
  children: ReactNode;
  saveLabel?: string;
  showFooter?: boolean;
}

const Modal = ({
  title,
  isOpen,
  onClose,
  onSave,
  onCancel,
  children,
  saveLabel = 'Save',
  showFooter = true,
}: ModalProps) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4">
      <div className="bg-white rounded-md shadow-xl max-w-md w-full max-h-[90vh] overflow-auto">
        <div className="flex justify-between items-center border-b p-4">
          <h2 className="text-lg font-medium">{title}</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <X size={20} />
          </button>
        </div>
        <div className="p-4">
          {children}
        </div>
        {showFooter && (
          <div className="flex justify-end gap-2 p-4 border-t">
            <Button
              variant="outline"
              onClick={onCancel || onClose}
              className="bg-red-500 hover:bg-red-600 text-white"
            >
              Cancel
            </Button>
            <Button
              onClick={onSave}
              className="bg-sems-primary hover:bg-sems-secondary text-white"
            >
              {saveLabel}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default Modal;
