
import { Link } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import Logo from '@/components/Logo';

const NotFound = () => {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-4">
      <div className="text-center">
        <Logo />
        <h1 className="mt-8 text-4xl font-bold text-gray-800">404</h1>
        <p className="mt-2 text-xl text-gray-600">Page not found</p>
        <p className="mt-4 text-gray-500">
          The page you are looking for doesn't exist or has been moved.
        </p>
        <div className="mt-6">
          <Link to="/admin">
            <Button className="bg-sems-primary hover:bg-sems-secondary text-white">
              Back to Dashboard
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default NotFound;
