import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import AdminLayout from '@/layouts/AdminLayout';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import FormInput from '@/components/FormInput';
import { api } from '@/utils/api';
import { toast } from '@/components/ui/sonner';

interface DeviceType {
  id: number;
  name: string;
  description: string;
}

const EditDeviceType = () => {
  const { deviceTypeId } = useParams<{ deviceTypeId: string }>();
  const [formData, setFormData] = useState<DeviceType>({
    id: 0,
    name: '',
    description: '',
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const navigate = useNavigate();

  useEffect(() => {
    fetchDeviceType();
  }, [deviceTypeId]);

  const fetchDeviceType = async () => {
    if (!deviceTypeId) return;
    
    setIsLoading(true);
    try {
      const data = await api.get(`/devicetype/${deviceTypeId}`);
      
      // Vérifier si la réponse contient directement les données ou si elles sont dans un sous-objet
      const deviceType = data.id !== undefined ? data : data.deviceType || {};
      
      setFormData({
        id: deviceType.id || 0,
        name: deviceType.name || '',
        description: deviceType.description || '',
      });
    } catch (error) {
      console.error('Error fetching device type:', error);
      toast.error('Failed to load device type');
    } finally {
      setIsLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      toast.error('Device type name is required');
      return;
    }
    
    try {
      setIsSubmitting(true);
      
      // Préparer les données selon le format attendu par l'API
      const updateData = {
        name: formData.name,
        description: formData.description
      };
      
      // Appel à l'API pour mettre à jour le type d'appareil
      await api.put(`/devicetype/${deviceTypeId}`, updateData);
      
      toast.success('Device type updated successfully');
      navigate('/admin/deviceTypes');
    } catch (error) {
      console.error('Error updating device type:', error);
      toast.error('Failed to update device type');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="bg-white rounded-md shadow-sm p-6">
          <p className="text-center">Loading device type information...</p>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="bg-white rounded-md shadow-sm p-6">
        <h2 className="text-xl font-medium mb-6">Edit Device Type</h2>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <FormInput
            label="Name"
            id="name"
            placeholder="Enter device type name"
            value={formData.name}
            onChange={(e) => handleChange(e)}
            name="name"
            required
          />
          
          <div>
            <label className="block mb-2">Description</label>
            <Textarea
              className="w-full h-32"
              placeholder="Enter device type description"
              name="description"
              value={formData.description}
              onChange={handleChange}
            />
          </div>
          
          <div className="flex justify-end gap-2">
            <Button
              type="button"
              className="bg-red-500 hover:bg-red-600 text-white"
              onClick={() => navigate('/admin/deviceTypes')}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="bg-sems-primary hover:bg-sems-secondary text-white"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Saving...' : 'Save Changes'}
            </Button>
          </div>
        </form>
      </div>
    </AdminLayout>
  );
};

export default EditDeviceType;
