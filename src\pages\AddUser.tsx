import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import AdminLayout from '@/layouts/AdminLayout';
import FormInput from '@/components/FormInput';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { apiRequest } from '@/utils/api';
import { toast } from '@/components/ui/sonner';

// Map the form role and status values to the API expected integer values
const roleMapping = {
  'Administrator': 0,
  'Client': 1
};

const statusMapping = {
  'Active': 0,
  'Locked': 1,
  'Disabled': 2
};

const AddUser = () => {
  const [formData, setFormData] = useState({
    login: '',
    role: 'Client',
    status: 'Active',
    password: '',
    confirmPassword: '',
    data: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const navigate = useNavigate();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Basic validation
    if (!formData.login.trim()) {
      toast.error('Login name is required');
      return;
    }
    
    if (!formData.password.trim()) {
      toast.error('Password is required');
      return;
    }
    
    if (formData.password !== formData.confirmPassword) {
      toast.error('Passwords do not match');
      return;
    }

    try {
      setIsSubmitting(true);
      
      // Validate JSON data if provided
      let jsonData = "{}";
      if (formData.data && formData.data.trim() !== '') {
        try {
          // Validate JSON format
          JSON.parse(formData.data);
          jsonData = formData.data;
        } catch (error) {
          toast.error('Invalid JSON format in data field');
          setIsSubmitting(false);
          return;
        }
      }
      
      // Prepare user data according to API specification
      const userData = {
        login: formData.login,
        role: roleMapping[formData.role as keyof typeof roleMapping],
        status: statusMapping[formData.status as keyof typeof statusMapping],
        password: formData.password,
        data: {
          jsonString: jsonData
        }
      };

      console.log('Sending user data:', userData);
      
      // Call API to create user directly with apiRequest
      await apiRequest('/user', {
        method: 'POST',
        body: JSON.stringify(userData)
      });
      
      toast.success('User created successfully');
      navigate('/admin/users');
    } catch (error: any) {
      console.error('Error creating user:', error);
      toast.error(`Failed to create user: ${error.message || 'Unknown error'}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <AdminLayout>
      <div className="bg-white rounded-md shadow-sm p-6">
        <h2 className="text-xl font-medium mb-6">Create New User</h2>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <FormInput
            label="Login"
            id="login"
            placeholder="Enter login name"
            value={formData.login}
            onChange={handleChange}
            name="login"
            required
          />
          
          <div>
            <label className="block mb-2">Role</label>
            <select
              className="w-full p-2 border rounded"
              name="role"
              value={formData.role}
              onChange={handleChange}
            >
              <option value="Administrator">Administrator</option>
              <option value="Client">Client</option>
            </select>
          </div>
          
          <div>
            <label className="block mb-2">Status</label>
            <select
              className="w-full p-2 border rounded"
              name="status"
              value={formData.status}
              onChange={handleChange}
            >
              <option value="Active">Active</option>
              <option value="Locked">Locked</option>
              <option value="Disabled">Disabled</option>
            </select>
          </div>
          
          <FormInput
            label="Password"
            id="password"
            type="password"
            placeholder="Enter password"
            value={formData.password}
            onChange={handleChange}
            name="password"
            required
          />
          
          <FormInput
            label="Confirm Password"
            id="confirmPassword"
            type="password"
            placeholder="Enter password again"
            value={formData.confirmPassword}
            onChange={handleChange}
            name="confirmPassword"
            required
          />
          
          <div>
            <label className="block mb-2">Data (JSON format)</label>
            <Textarea
              className="w-full h-32"
              placeholder='{"key": "value"}'
              name="data"
              value={formData.data}
              onChange={handleChange}
            />
          </div>
          
          <div className="flex justify-end gap-2">
            <Button
              type="button"
              className="bg-red-500 hover:bg-red-600 text-white"
              onClick={() => navigate('/admin/users')}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="bg-sems-primary hover:bg-sems-secondary text-white"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Creating...' : 'Save'}
            </Button>
          </div>
        </form>
      </div>
    </AdminLayout>
  );
};

export default AddUser;







