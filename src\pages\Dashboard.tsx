
import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import AdminLayout from '@/layouts/AdminLayout';
import { Users, Network, Cpu, Layers, PlugZap } from 'lucide-react';
import { toast } from '@/components/ui/sonner';

interface CountData {
  count: number;
}

interface DashboardCard {
  title: string;
  count: number;
  icon: React.ReactNode;
  link: string;
  color: string;
}

const Dashboard = () => {
  const [counts, setCounts] = useState({
    users: 0,
    networks: 0,
    devices: 0,
    deviceTypes: 0,
    plugins: 0,
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchCounts();
  }, []);

  const fetchCounts = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('sems-token');
      
      if (!token) {
        throw new Error('Authentication token not found');
      }

      // Fetch all counts in parallel
      const [usersCount, networksCount, devicesCount, deviceTypesCount, pluginsCount] = await Promise.all([
        fetchCount('user/count'),
        fetchCount('network/count'),
        fetchCount('device/count'),
        fetchCount('devicetype/count'),
        fetchCount('plugin/count'),
      ]);

      setCounts({
        users: usersCount,
        networks: networksCount,
        devices: devicesCount,
        deviceTypes: deviceTypesCount,
        plugins: pluginsCount,
      });
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      toast.error('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const fetchCount = async (endpoint: string): Promise<number> => {
    try {
      const token = localStorage.getItem('sems-token');
      const response = await fetch(`http://localhost:80/api/rest/${endpoint}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch ${endpoint}`);
      }

      const data: CountData = await response.json();
      return data.count;
    } catch (error) {
      console.error(`Error fetching ${endpoint}:`, error);
      return 0;
    }
  };

  const dashboardCards: DashboardCard[] = [
    {
      title: 'Users',
      count: counts.users,
      icon: <Users className="h-8 w-8 text-teal-600" />,
      link: '/admin/users',
      color: 'bg-teal-50',
    },
    {
      title: 'Networks',
      count: counts.networks,
      icon: <Network className="h-8 w-8 text-blue-600" />,
      link: '/admin/networks',
      color: 'bg-blue-50',
    },
    {
      title: 'Devices',
      count: counts.devices,
      icon: <Cpu className="h-8 w-8 text-purple-600" />,
      link: '/admin/devices',
      color: 'bg-purple-50',
    },
    {
      title: 'Device Types',
      count: counts.deviceTypes,
      icon: <Layers className="h-8 w-8 text-amber-600" />,
      link: '/admin/deviceTypes',
      color: 'bg-amber-50',
    },
    {
      title: 'Plugins',
      count: counts.plugins,
      icon: <PlugZap className="h-8 w-8 text-green-600" />,
      link: '/admin/plugins',
      color: 'bg-green-50',
    },
  ];

  return (
    <AdminLayout>
      <div className="space-y-6">
        <h1 className="text-2xl font-bold">Dashboard</h1>
        
        {loading ? (
          <div className="text-center py-10">Loading dashboard data...</div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {dashboardCards.map((card) => (
              <Link 
                key={card.title} 
                to={card.link}
                className="block"
              >
                <div className={`${card.color} rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow`}>
                  <div className="flex flex-col items-center text-center">
                    <div className="mb-4">
                      {card.icon}
                    </div>
                    <h3 className="text-lg font-medium mb-1">{card.title}</h3>
                    <p className="text-3xl font-bold">{card.count}</p>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        )}
      </div>
    </AdminLayout>
  );
};

export default Dashboard;

