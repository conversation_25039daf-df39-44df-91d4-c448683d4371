
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import AdminLayout from '@/layouts/AdminLayout';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import FormInput from '@/components/FormInput';
import { api } from '@/utils/api';
import { toast } from '@/components/ui/sonner';

const AddDeviceType = () => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const navigate = useNavigate();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      toast.error('Device type name is required');
      return;
    }
    
    try {
      setIsSubmitting(true);
      
      // Appel à l'API pour créer un nouveau type d'appareil
      const response = await api.post('/devicetype', {
        name: formData.name,
        description: formData.description
      });
      
      toast.success('Device type created successfully');
      navigate('/admin/deviceTypes');
    } catch (error) {
      console.error('Error creating device type:', error);
      toast.error('Failed to create device type');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <AdminLayout>
      <div className="bg-white rounded-md shadow-sm p-6">
        <h2 className="text-xl font-medium mb-6">Create New Device Type</h2>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <FormInput
            label="Name"
            id="name"
            placeholder="Enter device type name"
            value={formData.name}
            onChange={(e) => handleChange(e)}
            name="name"
            required
          />
          
          <div>
            <label className="block mb-2">Description</label>
            <Textarea
              className="w-full h-32"
              placeholder="Enter device type description"
              name="description"
              value={formData.description}
              onChange={handleChange}
            />
          </div>
          
          <div className="flex justify-end gap-2">
            <Button
              type="button"
              className="bg-red-500 hover:bg-red-600 text-white"
              onClick={() => navigate('/admin/deviceTypes')}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="bg-sems-primary hover:bg-sems-secondary text-white"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Creating...' : 'Save'}
            </Button>
          </div>
        </form>
      </div>
    </AdminLayout>
  );
};

export default AddDeviceType;

