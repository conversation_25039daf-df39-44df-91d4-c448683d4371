
import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { apiRequest } from '../utils/api';
import { AuthContextType, LoginResponse } from '../types';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [token, setToken] = useState<string | null>(null);
  const [refreshToken, setRefreshToken] = useState<string | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  useEffect(() => {
    // Check if token exists in AsyncStorage
    checkAuthState();
  }, []);

  const checkAuthState = async () => {
    try {
      const storedToken = await AsyncStorage.getItem('sems-token');
      const storedRefreshToken = await AsyncStorage.getItem('sems-refresh-token');

      if (storedToken) {
        setToken(storedToken);
        setRefreshToken(storedRefreshToken);
        setIsAuthenticated(true);
      }
    } catch (error) {
      console.error('Error checking auth state:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (username: string, password: string) => {
    try {
      const data: LoginResponse = await apiRequest('/auth/rest/token', {
        method: 'POST',
        body: JSON.stringify({
          login: username,
          password: password
        }),
      });

      // Store both tokens in AsyncStorage
      await AsyncStorage.setItem('sems-token', data.accessToken);
      await AsyncStorage.setItem('sems-refresh-token', data.refreshToken);

      // Update state
      setToken(data.accessToken);
      setRefreshToken(data.refreshToken);
      setIsAuthenticated(true);

    } catch (error) {
      console.error('Login error:', error);
      throw new Error('Authentication failed. Please check your credentials.');
    }
  };

  const logout = async () => {
    try {
      // Remove tokens from AsyncStorage
      await AsyncStorage.removeItem('sems-token');
      await AsyncStorage.removeItem('sems-refresh-token');

      // Update state
      setToken(null);
      setRefreshToken(null);
      setIsAuthenticated(false);
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  return (
    <AuthContext.Provider value={{ isAuthenticated, token, refreshToken, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}



