
import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { apiRequest } from '@/utils/api';

interface AuthContextType {
  isAuthenticated: boolean;
  token: string | null;
  refreshToken: string | null;
  login: (username: string, password: string) => Promise<void>;
  logout: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [token, setToken] = useState<string | null>(localStorage.getItem('sems-token'));
  const [refreshToken, setRefreshToken] = useState<string | null>(localStorage.getItem('sems-refresh-token'));
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(!!token);
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    // Check if token exists in localStorage
    const storedToken = localStorage.getItem('sems-token');
    if (storedToken) {
      setToken(storedToken);
      setIsAuthenticated(true);
    } else if (location.pathname !== '/login' && !location.pathname.startsWith('/public')) {
      // If no token and not on login page, redirect to login
      navigate('/login');
    }
  }, [navigate, location.pathname]);

  const login = async (username: string, password: string) => {
    try {
      const data = await apiRequest('/auth/rest/token', {
        method: 'POST',
        body: JSON.stringify({ 
          login: username, 
          password: password 
        }),
      });
      
      // Store both tokens in localStorage
      localStorage.setItem('sems-token', data.accessToken);
      localStorage.setItem('sems-refresh-token', data.refreshToken);
      
      // Update state
      setToken(data.accessToken);
      setRefreshToken(data.refreshToken);
      setIsAuthenticated(true);
      
      // Navigate to admin dashboard
      navigate('/admin');
    } catch (error) {
      console.error('Login error:', error);
      throw new Error('Authentication failed. Please check your credentials.');
    }
  };

  const logout = () => {
    // Remove tokens from localStorage
    localStorage.removeItem('sems-token');
    localStorage.removeItem('sems-refresh-token');
    
    // Update state
    setToken(null);
    setRefreshToken(null);
    setIsAuthenticated(false);
    
    // Navigate to login page
    navigate('/login');
  };

  return (
    <AuthContext.Provider value={{ isAuthenticated, token, refreshToken, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}



