
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import AdminLayout from '@/layouts/AdminLayout';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { api } from '@/utils/api';
import { toast } from '@/components/ui/sonner';

interface DeviceType {
  id: number;
  name: string;
  description: string;
}

interface CountResponse {
  count: number;
}

const DeviceTypes = () => {
  const [deviceTypes, setDeviceTypes] = useState<DeviceType[]>([]);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    setIsLoading(true);
    try {
      // Fetch device types and count in parallel
      const [typesData, countData] = await Promise.all([
        api.get<DeviceType[]>('/devicetype'),
        api.get<CountResponse>('/devicetype/count')
      ]);
      
      // Set device types
      const deviceTypesArray = Array.isArray(typesData) ? typesData : [];
      setDeviceTypes(deviceTypesArray);
      
      // Set total count
      setTotalCount(countData.count || 0);
    } catch (error) {
      console.error('Error fetching device types data:', error);
      toast.error('Failed to load device types');
      setDeviceTypes([]);
      setTotalCount(0);
    } finally {
      setIsLoading(false);
    }
  };

  const filteredDeviceTypes = deviceTypes.filter(deviceType =>
    deviceType.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleAddDeviceType = () => {
    navigate('/admin/deviceTypes/add');
  };

  const handleEditDeviceType = (deviceTypeId: number) => {
    navigate(`/admin/deviceTypes/${deviceTypeId}`);
  };

  const handleDeleteDeviceType = async (deviceTypeId: number) => {
    if (confirm('Are you sure you want to delete this device type?')) {
      try {
        await api.delete(`/devicetype/${deviceTypeId}`);
        toast.success('Device type deleted successfully');
        fetchData(); // Refresh the data
      } catch (error) {
        console.error('Error deleting device type:', error);
        toast.error('Failed to delete device type');
      }
    }
  };

  return (
    <AdminLayout
      actionButton={{
        label: 'Add New Device Type',
        onClick: handleAddDeviceType,
      }}
    >
      <div className="bg-white rounded-md shadow-sm">
        <div className="p-4 border-b flex justify-between items-center">
          <div className="flex items-center gap-2">
            <span>Search By Name:</span>
            <Input
              placeholder="Enter device type name"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="max-w-xs"
            />
          </div>
          <div className="text-sm text-gray-500">
            Total Device Types: <span className="font-medium">{totalCount}</span>
          </div>
        </div>
        <div className="overflow-x-auto">
          {isLoading ? (
            <div className="p-4 text-center">Loading device types...</div>
          ) : (
            <table className="w-full">
              <thead className="bg-gray-50 text-left">
                <tr>
                  <th className="p-4 font-medium">Name</th>
                  <th className="p-4 font-medium">Description</th>
                  <th className="p-4 font-medium">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y">
                {filteredDeviceTypes.length > 0 ? (
                  filteredDeviceTypes.map((deviceType) => (
                    <tr key={deviceType.id} className="hover:bg-gray-50">
                      <td className="p-4">{deviceType.name}</td>
                      <td className="p-4">{deviceType.description}</td>
                      <td className="p-4">
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            className="bg-blue-500 hover:bg-blue-600 text-white"
                            onClick={() => handleEditDeviceType(deviceType.id)}
                          >
                            Edit
                          </Button>
                          <Button
                            size="sm"
                            className="bg-red-500 hover:bg-red-600 text-white"
                            onClick={() => handleDeleteDeviceType(deviceType.id)}
                          >
                            Delete
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={3} className="p-4 text-center">
                      {searchTerm ? 'No device types match your search.' : 'No device types found.'}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          )}
        </div>
      </div>
    </AdminLayout>
  );
};

export default DeviceTypes;




