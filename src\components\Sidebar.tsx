
import { Link, useLocation } from 'react-router-dom';
import { cn } from '@/lib/utils';
import Logo from './Logo';
import { 
  LayoutDashboard, 
  Users, 
  Network, 
  Layers, 
  Cpu, 
  Key, 
  Plug
} from 'lucide-react';

const SidebarItem = ({ 
  icon: Icon, 
  label, 
  path, 
  active 
}: { 
  icon: React.ElementType; 
  label: string; 
  path: string; 
  active: boolean 
}) => {
  return (
    <Link to={path} className="block">
      <div 
        className={cn(
          "flex items-center gap-2 px-4 py-3 rounded-md text-gray-700 hover:bg-sems-light transition",
          active && "bg-sems-light text-sems-primary font-medium"
        )}
      >
        <Icon size={18} />
        <span>{label}</span>
      </div>
    </Link>
  );
};

const Sidebar = () => {
  const location = useLocation();
  const path = location.pathname;

  const isActive = (route: string) => {
    return path.startsWith(route);
  };

  const sidebarItems = [
    { icon: LayoutDashboard, label: 'Overview', path: '/admin' },
    { icon: Users, label: 'Users', path: '/admin/users' },
    { icon: Network, label: 'Networks', path: '/admin/networks' },
    { icon: Layers, label: 'Device Types', path: '/admin/deviceTypes' },
    { icon: Cpu, label: 'Devices', path: '/admin/devices' },
    { icon: Key, label: 'JWT Tokens', path: '/admin/jwt-tokens' },
    { icon: Plug, label: 'Plugins', path: '/admin/plugins' },
  ];

  return (
    <div className="w-56 h-screen bg-white border-r flex flex-col">
      <div className="p-4 border-b">
        <Logo />
      </div>
      <div className="p-2 flex-1">
        {sidebarItems.map((item, index) => (
          <SidebarItem
            key={index}
            icon={item.icon}
            label={item.label}
            path={item.path}
            active={isActive(item.path)}
          />
        ))}
      </div>
    </div>
  );
};

export default Sidebar;
