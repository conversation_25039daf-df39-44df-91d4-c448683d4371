import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import AdminLayout from '@/layouts/AdminLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { api } from '@/utils/api';
import { toast } from '@/components/ui/sonner';

interface UserFormData {
  login: string;
  role: number;
  status: number;
  password: string;
  confirmPassword: string;
  data: string;
}

const EditUser = () => {
  const { userId } = useParams<{ userId: string }>();
  const [formData, setFormData] = useState<UserFormData>({
    login: '',
    role: 1, // Default to Client
    status: 0, // Default to Active
    password: '',
    confirmPassword: '',
    data: '{}'
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    fetchUserDetails();
  }, [userId]);

  const fetchUserDetails = async () => {
    try {
      setIsLoading(true);
      const data = await api.get(`/user/${userId}`);
      
      setFormData({
        login: data.login || '',
        role: data.role || 1,
        status: data.status || 0,
        password: '',
        confirmPassword: '',
        data: data.data?.jsonString || '{}'
      });
    } catch (error) {
      console.error('Error fetching user details:', error);
      toast.error('Failed to load user details');
      navigate('/admin/users');
    } finally {
      setIsLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    if (name === 'role' || name === 'status') {
      setFormData(prev => ({ ...prev, [name]: parseInt(value) }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate form
    if (!formData.login.trim()) {
      toast.error('Username is required');
      return;
    }
    
    // Validate passwords match if provided
    if (formData.password && formData.password !== formData.confirmPassword) {
      toast.error('Passwords do not match');
      return;
    }
    
    try {
      setIsSubmitting(true);
      
      // Validate JSON data if provided
      let jsonData = "{}";
      if (formData.data && formData.data.trim() !== '') {
        try {
          // Validate JSON format
          JSON.parse(formData.data);
          jsonData = formData.data;
        } catch (error) {
          toast.error('Invalid JSON format in data field');
          return;
        }
      }
      
      // Prepare user data
      const userData: any = {
        login: formData.login,
        role: formData.role,
        status: formData.status,
        data: {
          jsonString: jsonData
        }
      };

      // Only include password if it was provided
      if (formData.password) {
        userData.password = formData.password;
      }
      
      // Update user
      await api.put(`/user/${userId}`, userData);
      
      toast.success('User updated successfully');
      navigate('/admin/users');
    } catch (error: any) {
      console.error('Error updating user:', error);
      toast.error(`Failed to update user: ${error.message || 'Unknown error'}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="bg-white rounded-md shadow-sm p-6 text-center">
          Loading user details...
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="bg-white rounded-md shadow-sm p-6">
        <h2 className="text-xl font-medium mb-6">Edit User</h2>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">Username</label>
            <Input
              name="login"
              value={formData.login}
              onChange={handleChange}
              className="w-full"
              required
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-1">Role</label>
            <select
              name="role"
              value={formData.role}
              onChange={handleChange}
              className="w-full p-2 border rounded"
            >
              <option value={0}>Administrator</option>
              <option value={1}>Client</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-1">Status</label>
            <select
              name="status"
              value={formData.status}
              onChange={handleChange}
              className="w-full p-2 border rounded"
            >
              <option value={0}>Active</option>
              <option value={1}>Locked</option>
              <option value={2}>Disabled</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-1">Password</label>
            <Input
              type="password"
              name="password"
              value={formData.password}
              onChange={handleChange}
              placeholder="Leave blank to keep unchanged"
              className="w-full"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-1">Confirm Password</label>
            <Input
              type="password"
              name="confirmPassword"
              value={formData.confirmPassword}
              onChange={handleChange}
              placeholder="Confirm password"
              className="w-full"
              disabled={!formData.password}
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-1">Data (JSON)</label>
            <Textarea
              name="data"
              value={formData.data}
              onChange={handleChange}
              className="w-full h-32 font-mono"
              placeholder="{}"
            />
          </div>
          
          <div className="flex justify-end space-x-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => navigate('/admin/users')}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="bg-sems-primary hover:bg-sems-secondary text-white"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Saving...' : 'Save Changes'}
            </Button>
          </div>
        </form>
      </div>
    </AdminLayout>
  );
};

export default EditUser;
