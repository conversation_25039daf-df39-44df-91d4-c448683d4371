
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { AuthProvider } from "@/contexts/AuthContext";
import ProtectedRoute from "@/components/ProtectedRoute";

import Login from "./pages/Login";
import Dashboard from "./pages/Dashboard";
import Users from "./pages/Users";
import UserDetails from "./pages/UserDetails";
import AddUser from "./pages/AddUser";
import EditUser from "./pages/EditUser";
import Networks from "./pages/Networks";
import AddNetwork from "./pages/AddNetwork";
import EditNetwork from "./pages/EditNetwork";
import DeviceTypes from "./pages/DeviceTypes";
import AddDeviceType from "./pages/AddDeviceType";
import EditDeviceType from "./pages/EditDeviceType";
import Devices from "./pages/Devices";
import AddDevice from "./pages/AddDevice";
import DeviceDetail from "./pages/DeviceDetail";
import JWTTokens from "./pages/JWTTokens";
import Plugins from "./pages/Plugins";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <BrowserRouter>
        <Toaster />
        <Sonner />
        <AuthProvider>
          <Routes>
            <Route path="/" element={<Navigate to="/login" replace />} />
            <Route path="/login" element={<Login />} />
            
            {/* Protected Routes */}
            <Route element={<ProtectedRoute />}>
              <Route path="/admin" element={<Dashboard />} />
              <Route path="/admin/users" element={<Users />} />
              <Route path="/admin/users/add" element={<AddUser />} />
              <Route path="/admin/users/:userId" element={<UserDetails />} />
              <Route path="/admin/users/:userId/edit" element={<EditUser />} />
              <Route path="/admin/networks" element={<Networks />} />
              <Route path="/admin/networks/add" element={<AddNetwork />} />
              <Route path="/admin/networks/:id" element={<EditNetwork />} />
              <Route path="/admin/deviceTypes" element={<DeviceTypes />} />
              <Route path="/admin/deviceTypes/add" element={<AddDeviceType />} />
              <Route path="/admin/deviceTypes/:deviceTypeId" element={<EditDeviceType />} />
              <Route path="/admin/devices" element={<Devices />} />
              <Route path="/admin/devices/add" element={<AddDevice />} />
              <Route path="/admin/devices/:deviceId" element={<DeviceDetail />} />
              <Route path="/admin/jwt-tokens" element={<JWTTokens />} />
              <Route path="/admin/plugins" element={<Plugins />} />
            </Route>
            
            <Route path="*" element={<NotFound />} />
          </Routes>
        </AuthProvider>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;







