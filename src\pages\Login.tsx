
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { apiRequest } from '@/utils/api';
import { toast } from '@/components/ui/sonner';
import Logo from '@/components/Logo';
import { Eye, EyeOff } from 'lucide-react';

const Login = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    
    try {
      const response = await apiRequest('/auth/rest/token', {
        method: 'POST',
        body: JSON.stringify({ 
          login: username, 
          password: password 
        }),
      });
      
      // Store both tokens in localStorage
      localStorage.setItem('sems-token', response.accessToken);
      localStorage.setItem('sems-refresh-token', response.refreshToken);
      
      // Success notification
      toast.success("Login successful");
      
      // Navigate to admin dashboard
      navigate('/admin');
    } catch (error) {
      toast.error("Authentication Failed: Invalid username or password. Please try again.");
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50">
      <div className="w-full max-w-md p-8 space-y-8 bg-white rounded-lg shadow-md">
        <div className="flex flex-col items-center space-y-2">
          <Logo />
          <h1 className="mt-4 text-2xl font-bold text-sems-primary">SEMS Admin Panel Login</h1>
          <p className="text-sm text-center text-gray-600">
            Smart Energy Management System by Smart Waves Technologies
          </p>
        </div>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-2">
            <label htmlFor="username" className="block text-sm font-medium text-gray-700">
              Username
            </label>
            <Input
              id="username"
              placeholder="Enter your username"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              className="w-full px-3 py-2 border rounded-md"
              required
            />
          </div>
          
          <div className="space-y-2">
            <label htmlFor="password" className="block text-sm font-medium text-gray-700">
              Password
            </label>
            <div className="relative">
              <Input
                id="password"
                type={showPassword ? "text" : "password"}
                placeholder="Enter your password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-3 py-2 border rounded-md"
                required
              />
              <button 
                type="button"
                className="absolute inset-y-0 right-0 flex items-center pr-3"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
              </button>
            </div>
          </div>
          
          <Button 
            type="submit" 
            className="w-full bg-sems-primary hover:bg-sems-primary/90 text-white h-11"
            disabled={isLoading}
          >
            {isLoading ? 'Logging in...' : 'Login'}
          </Button>
        </form>
      </div>
      <div className="mt-4 text-sm text-gray-600">
        Copyright © {new Date().getFullYear()} | Smart Waves Technologies
      </div>
    </div>
  );
};

export default Login;





